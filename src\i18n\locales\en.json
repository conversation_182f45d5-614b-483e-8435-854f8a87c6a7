{"navigation": {"home": "Home", "about": "About", "projects": "Projects", "contact": "Contact"}, "hero": {"name": "RAKOTOZANANY Reald <PERSON>ne", "title": "Frontend Developer specializing in React.js & TypeScript", "description": "Building modern web applications with a focus on performance and user experience."}, "about": {"title": "About Me", "intro": "👋 Hey, I'm <PERSON><PERSON>! A passionate frontend developer who loves turning ideas into sleek, interactive experiences. My go-to tool? React! I'm always exploring best practices to build modern and high-performance user interfaces.", "technologies": "Technologies", "frontend": "Frontend", "styling": "Styl<PERSON>", "stateApi": "State & API", "tools": "Tools", "currentExperience": "Current Experience", "internTitle": "Frontend Developer Intern", "internCompany": "Steedy digital Solutions", "internDuration": "6 months internship", "internDescription": "Currently working on innovative web solutions, focusing on React development and modern frontend practices. This experience has been instrumental in applying theoretical knowledge to real-world projects and working in a professional development environment.", "achievement1": "Strengthened my skills in React and state management.", "achievement2": "Optimized API calls for better performance.", "achievement3": "Collaborated with backend developers, gaining insights into API-first architectures.", "whatDrivesMe": "What Drives Me", "motivation1": "I'm always on the lookout for ways to improve and level up my skills. Right now, I'm diving deeper into advanced React concepts to push my frontend development further.", "motivation2": "Inspired by epic tales and Chinese culture 🏮, I see web development as a legendary journey—one filled with learning, challenges, and creative problem-solving—just like <PERSON>'s adventures.", "letsConnect": "Let's Work Together!", "connectDescription": "Looking for a motivated, creative developer ready to take on new challenges? Let's connect and bring your ideas to life! 🚀"}, "skills": {"title": "My Skills", "subtitle": "An overview of my technical skills and expertise level in each technology.", "categories": {"frontend": "Frontend", "styling": "Styl<PERSON>", "stateApi": "State & API", "tools": "Tools"}, "level": "Level", "beginner": "<PERSON><PERSON><PERSON>", "expert": "Expert", "evolution": {"title": "Constantly Evolving", "description": "I continue to learn and master new technologies to stay at the forefront of modern web development.", "learning": "learning"}}, "projects": {"title": "My Projects", "solar": {"title": "Solar - Energy Dashboard", "description": "Solar is a web application developed with React to visualize and manage solar energy production and consumption data. Connected to a Django Rest Framework backend, it offers a smooth and intuitive interface allowing users to monitor their solar installations in real time."}, "musicPlayer": {"title": "Music Player", "description": "Modern music player application with intuitive interface."}}, "testimonials": {"title": "What they say about me", "subtitle": "Discover testimonials from my clients and collaborators about the quality of my work.", "at": "at", "cta": {"title": "Ready to collaborate?", "description": "Join the list of my satisfied clients and let's turn your ideas into reality!", "button": "Start a project"}, "clients": [{"name": "<PERSON>", "role": "Project Manager", "company": "TechCorp", "content": "<PERSON><PERSON> was an excellent collaborator on our project. His React expertise and ability to deliver quality solutions on time were remarkable. I highly recommend him!", "rating": 5}, {"name": "<PERSON>", "role": "Lead Developer", "company": "StartupXYZ", "content": "Working with <PERSON><PERSON> was a very positive experience. He quickly understands requirements and proposes innovative solutions. His code is clean and well documented.", "rating": 5}, {"name": "<PERSON>", "role": "UX Designer", "company": "DesignStudio", "content": "<PERSON><PERSON> has an excellent understanding of user experience. He was able to transform our mockups into functional and aesthetic interfaces. A true professional!", "rating": 5}]}, "contact": {"title": "Contact Me", "subtitle": "Ready to collaborate on your next project? Feel free to reach out!", "contactInfo": "Contact Information", "followMe": "Follow Me", "sendMessage": "Send me a message", "form": {"name": "Full Name", "email": "Email", "subject": "Subject", "message": "Message", "send": "Send Message", "sending": "Sending...", "success": "Message sent successfully!", "error": "Error sending message.", "placeholders": {"name": "Your name", "email": "<EMAIL>", "subject": "Subject of your message", "message": "Describe your project or request..."}}, "info": {"email": "Email", "phone": "Phone", "location": "Location"}}, "footer": {"contact": "Contact", "connect": "Connect", "copyright": "© 2024 RAKOTOZANANY Reald <PERSON>. All rights reserved."}}