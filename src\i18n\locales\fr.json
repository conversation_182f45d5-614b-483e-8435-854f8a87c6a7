{"navigation": {"home": "Accueil", "about": "À propos", "projects": "Projets", "contact": "Contact"}, "hero": {"name": "RAKOTOZANANY Reald <PERSON>ne", "title": "Développeur Frontend spécialisé en React.js & TypeScript", "description": "Création d'applications web modernes avec un focus sur la performance et l'expérience utilisateur."}, "about": {"title": "À propos de moi", "intro": "👋 Salut, je suis Reald ! Un développeur frontend passionné qui aime transformer les idées en expériences élégantes et interactives. Mon outil de prédilection ? React ! J'explore constamment les meilleures pratiques pour construire des interfaces utilisateur modernes et performantes.", "technologies": "Technologies", "frontend": "Frontend", "styling": "Styl<PERSON>", "stateApi": "State & API", "tools": "Outils", "currentExperience": "Expérience Actuelle", "internTitle": "Stagiaire Développeur Frontend", "internCompany": "Steedy digital Solutions", "internDuration": "Stage de 6 mois", "internDescription": "Actuellement en train de travailler sur des solutions web innovantes, en me concentrant sur le développement React et les pratiques frontend modernes. Cette expérience a été déterminante pour appliquer les connaissances théoriques à des projets concrets et travailler dans un environnement de développement professionnel.", "achievement1": "<PERSON><PERSON><PERSON><PERSON> mes compétences en React et gestion d'état.", "achievement2": "Optimisé les appels API pour de meilleures performances.", "achievement3": "Collaboré avec des développeurs backend, acquérant des connaissances sur les architectures API-first.", "whatDrivesMe": "Ce qui me motive", "motivation1": "Je suis toujours à la recherche de moyens d'améliorer et de faire progresser mes compétences. En ce moment, j'approfondis les concepts React avancés pour pousser mon développement frontend plus loin.", "motivation2": "Inspiré par les contes épiques et la culture chinoise 🏮, je vois le développement web comme un voyage légendaire—rempli d'apprentissage, de défis et de résolution créative de problèmes—tout comme les aventures de Sun Wukong.", "letsConnect": "Travaillons ensemble !", "connectDescription": "Vous cherchez un développeur motivé et créatif prêt à relever de nouveaux défis ? Connectons-nous et donnons vie à vos idées ! 🚀"}, "skills": {"title": "Mes Compétences", "subtitle": "Un aperçu de mes compétences techniques et de mon niveau d'expertise dans chaque technologie.", "categories": {"frontend": "Frontend", "styling": "Styl<PERSON>", "stateApi": "State & API", "tools": "Outils"}, "level": "Niveau", "beginner": "Débutant", "expert": "Expert", "evolution": {"title": "En constante évolution", "description": "Je continue d'apprendre et de maîtriser de nouvelles technologies pour rester à la pointe du développement web moderne.", "learning": "en cours"}}, "projects": {"title": "Mes Projets", "solar": {"title": "Solar - Dashboard Énergie", "description": "Solar est une application web développée avec React pour visualiser et gérer les données de production et de consommation d'énergie solaire. Connectée à un backend en Django Rest Framework, elle offre une interface fluide et intuitive permettant aux utilisateurs de surveiller en temps réel leurs installations solaires."}, "musicPlayer": {"title": "Music Player", "description": "Application de lecture musicale moderne avec interface intuitive."}}, "testimonials": {"title": "Ce qu'ils disent de moi", "subtitle": "Découvrez les témoignages de mes clients et collaborateurs sur la qualité de mon travail.", "at": "chez", "cta": {"title": "<PERSON>rêt à collaborer ?", "description": "Rejoignez la liste de mes clients satisfaits et transformons vos idées en réalité !", "button": "Commencer un projet"}, "clients": [{"name": "<PERSON>", "role": "Chef de Projet", "company": "TechCorp", "content": "Reald a été un excellent collaborateur sur notre projet. Son expertise en React et sa capacité à livrer des solutions de qualité dans les délais ont été remarquables. Je recommande vivement !", "rating": 5}, {"name": "<PERSON>", "role": "Lead Developer", "company": "StartupXYZ", "content": "Travailler avec Reald a été une expérience très positive. Il comprend rapidement les besoins et propose des solutions innovantes. Son code est propre et bien documenté.", "rating": 5}, {"name": "<PERSON>", "role": "UX Designer", "company": "DesignStudio", "content": "Reald a une excellente compréhension de l'expérience utilisateur. Il a su transformer nos maquettes en interfaces fonctionnelles et esthétiques. Un vrai professionnel !", "rating": 5}]}, "contact": {"title": "<PERSON>ez-moi", "subtitle": "Prêt à collaborer sur votre prochain projet ? N'hésitez pas à me contacter !", "contactInfo": "Informations de contact", "followMe": "<PERSON><PERSON><PERSON><PERSON>moi", "sendMessage": "Envoyez-moi un message", "form": {"name": "Nom complet", "email": "Email", "subject": "Sujet", "message": "Message", "send": "Envoyer le message", "sending": "Envoi en cours...", "success": "Message envoyé avec succès !", "error": "Erreur lors de l'envoi du message.", "placeholders": {"name": "Votre nom", "email": "<EMAIL>", "subject": "Sujet de votre message", "message": "Décrivez votre projet ou votre demande..."}}, "info": {"email": "Email", "phone": "Téléphone", "location": "Localisation"}}, "footer": {"contact": "Contact", "connect": "Réseaux", "copyright": "© 2024 RAKOTOZANANY Reald Rigane. Tous droits réservés."}}