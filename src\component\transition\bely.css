

@import url('https://fonts.googleapis.com/css2?family=<PERSON><PERSON>&display=swap');

* {
  box-sizing: border-box;
}

body {
  font-family: 'Judson', serif;
  background: linear-gradient(
    to bottom,
    rgba(99, 167, 191, 1),
    rgba(94, 86, 179, 1),
    rgba(72, 139, 163, 1),
    rgba(79, 124, 179, 1),
    rgba(52, 115, 140, 1),
    rgba(48, 92, 145, 1),
    rgba(39, 82, 145, 1),
    rgba(19, 58, 110, 1),
    rgba(10, 50, 80, 1),
    rgba(40, 41, 100, 1),
    rgba(14, 52, 94, 1),
    black
  );
  max-width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  color: white;
  position: relative;
  margin: 0;
}

body::after {
  position: fixed;
  content: '';
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: radial-gradient(circle at center, transparent, rgba(0, 0, 0, 0.5));
}

@media (min-width: 40em) {
  body {
    font-size: 2rem;
  }
}

/* RAYS */
.rays {
  --r: 10deg;
  --c: rgba(255, 251, 227, 0.2);
  --size: max(60vh, 80rem);
  --mask: radial-gradient(circle at center, black, transparent 50%);
  position: fixed;
  pointer-events: none;
  top: calc(var(--size) * -0.55);
  left: 50%;
  width: var(--size);
  height: var(--size);
}
.rays > div {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: repeating-conic-gradient(var(--c), var(--c) var(--r), transparent var(--r), transparent calc(var(--r) * 2));
  -webkit-mask-image: var(--mask);
  mask-image: var(--mask);
  /* animation: raysRotate 120000ms linear infinite; */
}

@keyframes raysRotate {
  50% {
    transform: rotate(180deg) scale(1.5);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* FISH WRAPPER */
.fish-wrapper {
  --mask: linear-gradient(180deg, rgba(0, 0, 0, 1.0), transparent);
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  perspective: 100rem;
  perspective-origin: center center;
  transform-style: preserve-3d;
  pointer-events: none;
  -webkit-mask-image: var(--mask);
  mask-image: var(--mask);
  z-index: 2;
}

/* FISH */
.fish {
  --bodyW: 2rem;
  --o: 0.95;
  --l: 100%;
  --c: hsla(250deg, 50%, var(--l), var(--o, 0.6));
  --size: 10rem;
  position: relative;
  width: var(--size);
  height: var(--size);
  transform-style: preserve-3d;
  transform-origin: center;
}

@media (min-width: 1000px) {
  .fish {
    --size: 20rem;
    --bodyW: 4rem;
  }
}

/* FISH SKELETON */
.fish__skeleton {
  --clip: polygon(0 var(--bodyW), 45% 0, 55% 0, 100% var(--bodyW), 50% 100%);
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    repeating-linear-gradient(0deg, var(--c), var(--c) 0.1rem, transparent 0, transparent 0.5rem),
    linear-gradient(var(--c) var(--bodyW), transparent var(--bodyW)),
    linear-gradient(90deg, transparent calc((var(--bodyW) / 2) - 0.1rem), var(--c) 0, var(--c) calc((var(--bodyW) / 2) + 0.1rem), transparent 0);
  top: calc(var(--bodyW) / 4);
  left: calc(var(--bodyW) * 0.75);
  width: var(--bodyW);
  height: calc(var(--bodyW) * 4);
  -webkit-clip-path: var(--clip);
  clip-path: var(--clip);
  opacity: 0;
  transform: translate3d(0, 0, calc(var(--bodyW) / -2)) rotate(90deg);
  transform-origin: center center;
}

/* FISH INNER */
.fish__inner {
  --a: 9.5deg;
  width: calc(var(--bodyW) * 1.5);
  height: var(--size);
  transform-style: preserve-3d;
  transform: rotate(90deg);
}

/* FISH BODY */
.fish__body {
  --l: 75%;
  --c: hsla(250deg, 50%, var(--l), var(--o, 0.6));
  position: absolute;
  top: var(--bodyW);
  left: 0;
  width: var(--bodyW);
  height: calc(3 * var(--bodyW));
  background: var(--c);
  clip-path: polygon(0 0, 100% 0, 50% 100%);
  transform: translateZ(calc(var(--bodyW) / -2)) rotateX(var(--a));
  transform-origin: center top;
}
.fish__body:nth-child(2) {
  --i: 2;
  --l: 75%;
  transform: translateZ(calc(var(--bodyW) / 2)) rotateX(calc(var(--a) * -1));
}
.fish__body:nth-child(3) {
  --i: 3;
  --l: 95%;
  transform: rotateY(90deg) translate3d(calc(var(--bodyW) / -2), 0, 0) rotateX(var(--a));
  transform-origin: left top;
}
.fish__body:nth-child(4) {
  --i: 4;
  --l: 50%;
  transform: rotateY(90deg) translate3d(calc(var(--bodyW) / 2), 0, 0) rotateX(calc(var(--a) * -1));
  transform-origin: right top;
}

/* FISH HEAD */
.fish__head {
  --a: 23.5deg;
  --l: 85%;
  --c: hsla(250deg, 50%, var(--l), var(--o, 0.6));
  position: absolute;
  top: 0;
  left: 0;
  width: var(--bodyW);
  height: var(--bodyW);
  background: var(--c);
  clip-path: polygon(40% 0, 60% 0, 100% 100%, 0 100%);
  transform: translateZ(calc(var(--bodyW) / 2)) rotateX(var(--a));
  transform-origin: center bottom;
}
.fish__head--2 {
  --i: 2;
  --l: 80%;
  transform: translateZ(calc(var(--bodyW) / -2)) rotateX(calc(var(--a) * -1));
}
.fish__head--3 {
  --i: 3;
  --l: 90%;
  transform: rotateY(90deg) translate3d(calc(var(--bodyW) / -2), 0, 0) rotateX(calc(var(--a) * -1));
  transform-origin: left bottom;
}
.fish__head--4 {
  --l: 55%;
  transform: rotateY(90deg) translate3d(calc(var(--bodyW) / 2), 0, 0) rotateX(var(--a));
  transform-origin: right bottom;
}

/* FISH TAIL */
.fish__tail-main {
  --o: 0.9;
  --l: 90%;
  --c: hsla(250deg, 50%, var(--l), var(--o, 0.6));
  width: var(--bodyW);
  height: var(--bodyW);
  background-color: var(--c);
  position: absolute;
  left: 0;
  bottom: var(--bodyW);
  clip-path: polygon(50% 0, 100% 100%, 0 100%);
}
.fish__tail-fork {
  --o: 0.9;
  --l: 95%;
  --c: hsla(250deg, 50%, var(--l), var(--o, 0.6));
  width: var(--bodyW);
  height: var(--bodyW);
  background-color: var(--c);
  position: absolute;
  left: 0;
  bottom: 0;
  clip-path: polygon(0 0, 100% 0, 100% 70%, 90% 100%, 70% 70%, 50% 30%, 30% 70%, 10% 100%, 0 70%);
  transform-origin: top center;
  transform: rotateX(-45deg);
  animation: tail 1000ms infinite alternate;
}

@keyframes tail {
  to {
    transform: rotateX(45deg);
  }
}

/* FISH FIN */
.fish__fin {
  width: calc(var(--bodyW) / 8 * 3);
  height: var(--bodyW);
  background-color: var(--c);
  position: absolute;
  top: calc(var(--bodyW) * 1.5);
  left: calc(var(--bodyW) / 8 * 3);
  clip-path: polygon(50% 0, 100% 30%, 100% 60%, 50% 100%, 0 60%, 0 30%);
  transform-origin: top center;
  transform: translateZ(calc(var(--bodyW) / 2)) rotateY(0deg) rotateX(5deg) rotate(10deg);
  animation: fin 1500ms infinite alternate linear;
}
.fish__fin--2 {
  transform: translateZ(calc(var(--bodyW) / -2)) rotateY(0deg) rotateX(-5deg) rotate(10deg);
  animation: fin2 1500ms infinite alternate linear;
}

@keyframes fin {
  100% {
    transform: translateZ(calc(var(--bodyW) / 2)) rotateY(10deg) rotateX(20deg) rotate(-10deg);
  }
}

@keyframes fin2 {
  100% {
    transform: translateZ(calc(var(--bodyW) / -2)) rotateY(-10deg) rotateX(-20deg) rotate(-10deg);
  }
}

/* LIGHTS */
.lights {
  position: fixed;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
}
.lights__group {
  position: relative;
  height: 100%;
}
.lights__light {
  --size: 0.35rem;
  width: var(--size);
  height: var(--size);
  position: absolute;
  background: rgba(255, 255, 255, 1);
  border-radius: 100%;
  top: 10%;
  left: 25%;
  filter: blur(0.1rem);
  animation: blink 2500ms var(--d, 0ms) infinite alternate;
}
.lights__light:nth-child(2) {
  --d: 200ms;
  top: 40%;
  left: 12%;
}
.lights__light:nth-child(3) {
  --d: 350ms;
  top: 60%;
  left: 18%;
}
.lights__light:nth-child(4) {
  --d: 600ms;
  top: 25%;
  left: 66%;
}
.lights__light:nth-child(5) {
  --d: 1210ms;
  top: 43%;
  left: 55%;
}
.lights__light:nth-child(6) {
  --d: 420ms;
  top: 90%;
  left: 37%;
}
.lights__light:nth-child(7) {
  --d: 1100ms;
  top: 82%;
  left: 91%;
}
.lights__light:nth-child(8) {
  --d: 1560ms;
  top: 67%;
  left: 81%;
}

@keyframes blink {
  to {
    opacity: 0;
  }
}

/* CONTENT */
.content {
  position: relative;
  z-index: 1;
  padding-bottom: 100vh;
}
section {
  height: 100vh;
  width: 100%;
  margin-top: 100vh;
}
section:nth-child(4n),
section:nth-child(4n - 1) {
  --col: 3;
}
.section__content {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}
@media (min-width: 50rem) {
  .section__content {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 25rem));
    gap: 2rem;
    padding: 3rem;
  }
  .section__content > * {
    grid-column: var(--col, 1);
    opacity: 0;
  }
}

/* BUBBLES */
.bubbles {
  position: fixed;
  top: 0;
  left: 5rem;
  transform-origin: center;
  transform: translate3d(10rem, 5rem, 0) rotateX(20deg) rotateY(0deg);
}
.bubbles__inner {
  width: 10rem;
  height: 10rem;
}
.bubbles__bubble {
  --c: rgba(255, 255, 255, 0.4);
  --size: 2.5rem;
  position: absolute;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  background: radial-gradient(transparent 30%, var(--c, white)), radial-gradient(circle at 100% 0%, transparent 30%, var(--c, white));
  transform-origin: center;
  transform: scale(0);
  opacity: 0;
}
.bubbles__bubble:nth-child(2) {
  --size: 1.8rem;
  top: 3rem;
  left: 2rem;
}
.bubbles__bubble:nth-child(3) {
  --size: 1.2rem;
  top: 6rem;
  left: 0;
}

/* INDICATOR */
.indicator {
  text-align: center;
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translate3d(-50%, 0, 0);
  font-size: 1.2rem;
}
.indicator span {
  display: block;
}
.indicator span:nth-child(2) {
  animation: arrowMove 600ms infinite alternate;
}

@keyframes arrowMove {
  to {
    transform: translate3d(0, 0.5rem, 0);
  }
}
