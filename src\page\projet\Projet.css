/* Animations et effets personnalisés pour les projets */

/* Animation de pulsation pour les badges */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Effet de brillance sur les cartes */
.card-shine {
  position: relative;
  overflow: hidden;
}

.card-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.card-shine:hover::before {
  left: 100%;
}

/* Animation de flottement */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Effet de gradient animé */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animated-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Effet de zoom sur les images */
.image-zoom {
  transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.image-zoom:hover {
  transform: scale(1.1);
}

/* Effet de bordure animée */
@keyframes border-glow {
  0%, 100% {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);
  }
  50% {
    border-color: rgba(59, 130, 246, 0.8);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
}

.border-glow {
  animation: border-glow 2s ease-in-out infinite;
}

/* Effet de particules flottantes */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.7;
  }
}

.particle {
  animation: particle-float 4s ease-in-out infinite;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .card-shine::before {
    display: none;
  }
  
  .float-animation {
    animation: none;
  }
}
