{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@gsap/react": "^2.1.2", "@react-three/drei": "^9.48.6", "@react-three/fiber": "^8.17.14", "@tailwindcss/vite": "^4.0.0", "framer-motion": "^12.0.6", "gsap": "^3.12.7", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.474.0", "maath": "^0.10.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-i18next": "^15.5.3", "react-icons": "^5.4.0", "react-router-dom": "^7.1.3", "tailwindcss": "^4.0.0", "three-mesh-bvh": "^0.8.0", "three-stdlib": "^2.35.13"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/three": "^0.173.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "terser": "^5.43.1", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}